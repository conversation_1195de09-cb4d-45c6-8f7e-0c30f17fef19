CREATE TABLE `system_settings` (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `oper_user_id` bigint NOT NULL COMMENT '创建用户ID',
                                   `setting_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'key',
                                   `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'value',
                                   `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
                                   `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `ss_lt` (`last_update_time`) USING BTREE,
                                   KEY `ss_key` (`setting_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='系统参数表';


INSERT INTO `oper_auth_resource` (`id`, `resource_code`, `resource_name`, `create_time`, `operator_id`, `last_update_time`) VALUES (108, 'SYSTEM_SETTINGS_VIEW', 'vc', '2025-07-07 20:08:14', 1, '2025-07-07 20:08:33');
INSERT INTO `oper_auth_role_resource_assign` ( `resource_id`, `role_id`, `create_time`, `operator_id`, `last_update_time`) VALUES ( 108, 1, '2025-07-07 20:08:54', 1, '2025-07-07 20:08:58');
