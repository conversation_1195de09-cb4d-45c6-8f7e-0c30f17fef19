<template>
  <div class="app-container">
    <el-form
      ref="configForm"
      :model="configForm"
      status-icon
      :rules="formRules"
      label-width="160px"
      size="mini"
      class="configForm"
    >

      <el-row class="module">
        <el-col :span="4">过滤黑域名</el-col>
        <el-col :span="20" style="line-height: 30px;">
          <el-row :gutter="20">
            <el-col :span="16">
              <el-form-item label="黑域名列表：">
                <el-input
                  v-model="configForm.blackDomainList"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入黑域名，每行一个或使用英文逗号分隔"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4"><el-button type="primary" @click="setdata(['blackDomainList'])">保存</el-button></el-col>
          </el-row>
        </el-col>
      </el-row>

    </el-form>
  </div>

</template>

<script>
import { get, set } from '@/api/system/settings.js'// 接口
export default {
  name: 'SettingsManage',
  data() {
    return {
      configForm: {
        blackDomainList: null
      },
      formRules: {}
    }
  },
  created() {
    this.getdata()
  },
  methods: {
    getdata() {
      get().then(response => {
        this.configForm = response.result
      })
    },
    setdata(keyArrays) {
      const sumitData = {}
      for (const i in keyArrays) {
        const key = keyArrays[i]
        sumitData[key] = this.configForm[key] + ''
      }
      var jsonStr = JSON.stringify(sumitData)
      set({ 'jsonString': jsonStr }).then(response => {
        if (response.success === true) {
          this.$message({
            type: 'success',
            message: '更新成功!'
          })
        }
      })
    }
  }
}
</script>

<style>
/* 模块 */
.module {
  margin-top: 20px;
  border: 1px solid #e2dfdf;
  padding: 20px;
  border-radius: 0px;
}
.button-new-tag {
  margin-left: 10px;
  height: 24px;
  line-height: 24px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
