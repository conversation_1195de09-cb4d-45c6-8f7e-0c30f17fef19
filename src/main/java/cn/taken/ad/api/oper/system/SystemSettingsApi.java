package cn.taken.ad.api.oper.system;

import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.constant.web.WebAuth;
import cn.taken.ad.core.pojo.system.SystemSettings;
import cn.taken.ad.core.service.system.OperLogService;
import cn.taken.ad.core.service.system.SystemSettingsService;
import cn.taken.ad.utils.web.OperWebUtils;
import com.google.gson.reflect.TypeToken;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统参数API
 */
@RestController
@RequestMapping(value = "/o/systemSettings", method = RequestMethod.POST)
public class SystemSettingsApi {
    @Resource
    private OperLogService operLogService;
    @Resource
    private SystemSettingsService systemSettingsService;

    /**
     * 系统参数
     */
    @WebAuth("SYSTEM_SETTINGS_VIEW")
    @RequestMapping("/get")
    public SuperResult<Map<String, String>> info() {
        List<SystemSettings> settings = systemSettingsService.findAll();
        Map<String, String> itemMap = new HashMap<>();
        for (SystemSettings systemSettings : settings) {
            itemMap.put(systemSettings.getSettingKey(), systemSettings.getSettingValue());
        }
        return SuperResult.rightResult(itemMap);
    }

    /**
     * 修改系统参数
     */
    @WebAuth("SYSTEM_SETTINGS_VIEW")
    @RequestMapping("/set")
    public SuperResult<String> set(String jsonString) {
        if (jsonString == null) {
            return SuperResult.badResult("参数错误");
        }
        Map<String, String> settings = JsonHelper.fromJson(new TypeToken<Map<String, String>>() {
        }, jsonString);
        if (settings == null) {
            return SuperResult.badResult("参数未找到");
        }
        boolean writeOper = true;
        for (Map.Entry<String, String> entry : settings.entrySet()) {
        }

        settings.forEach((key, value) -> {
            systemSettingsService.update(key, value);
        });
        if (writeOper) {
            operLogService.saveOperLog("系统任务", "修改系统配置", OperWebUtils.getWebToken().getUserId());
        } else {
            operLogService.saveOperLog("系统任务", "修改系统配置", OperWebUtils.getWebToken().getUserId());
        }
        return SuperResult.rightResult();
    }




}
