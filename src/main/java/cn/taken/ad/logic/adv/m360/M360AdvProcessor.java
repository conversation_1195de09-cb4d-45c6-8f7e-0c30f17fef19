package cn.taken.ad.logic.adv.m360;

import cn.taken.ad.component.http.apache.FastHttpClient;
import cn.taken.ad.component.http.apache.HttpResult;
import cn.taken.ad.component.json.JsonHelper;
import cn.taken.ad.component.utils.encryption.Md5;
import cn.taken.ad.component.utils.result.SuperResult;
import cn.taken.ad.component.utils.string.StringUtils;
import cn.taken.ad.constant.business.*;
import cn.taken.ad.constant.logic.LogicState;
import cn.taken.ad.constant.logic.LogicSuffix;
import cn.taken.ad.logic.AdvProcessor;
import cn.taken.ad.logic.adv.m360.dto.BidRequest;
import cn.taken.ad.logic.adv.m360.dto.BidResponse;
import cn.taken.ad.logic.adv.m360.dto.req.Adspace;
import cn.taken.ad.logic.adv.m360.dto.req.App;
import cn.taken.ad.logic.adv.m360.dto.req.Device;
import cn.taken.ad.logic.adv.m360.dto.req.DeviceId;
import cn.taken.ad.logic.adv.m360.dto.resp.Ads;
import cn.taken.ad.logic.adv.m360.dto.resp.Img;
import cn.taken.ad.logic.adv.m360.dto.resp.Navtive;
import cn.taken.ad.logic.adv.m360.dto.resp.Video;
import cn.taken.ad.logic.base.rtb.RtbAdvDto;
import cn.taken.ad.logic.base.rtb.RtbReqAdvBillParam;
import cn.taken.ad.logic.base.rtb.request.RtbRequestDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestAppDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestDeviceDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestNetworkDto;
import cn.taken.ad.logic.base.rtb.request.dto.RequestTagDto;
import cn.taken.ad.logic.base.rtb.response.RtbResponseDto;
import cn.taken.ad.logic.base.rtb.response.dto.*;
import cn.taken.ad.utils.ListUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * https://easydoc.soft.360.cn/doc?project=9aa82f66a2bcc7a3720b2c56fcd8da24&doc=12647f52e83de617ead3762ab7a7bd4d&config=title_menu_toc
 */
@Component("M360" + LogicSuffix.ADV_LOGIC_SUFFIX)
public class M360AdvProcessor implements AdvProcessor {

    @Override
    public RtbResponseDto reqAdv(FastHttpClient httpClient, RtbRequestDto rtbDto, RtbAdvDto advDto) throws Throwable {
        BidRequest request = new BidRequest();
        request.setBid(Md5.md5(rtbDto.getReqId()).toLowerCase());
        request.setApp(createApp(rtbDto));
        request.setDevice(createDevice(rtbDto, rtbDto.getNetwork()));
        request.setAdspaces(createAdspaces(rtbDto, advDto));
        request.setUid(Md5.md5(rtbDto.getReqId() + String.format("%03d", (int) (Math.random() * 1000))).toLowerCase());
        request.setIp(rtbDto.getNetwork().getIp());
        request.setIpv6(rtbDto.getNetwork().getIpv6());
        request.setUser_agent(rtbDto.getDevice().getUserAgent());
        if (rtbDto.getUser() != null && rtbDto.getUser().getInterest() != null) {
            request.setUtag(StringUtils.join(rtbDto.getUser().getInterest(), ","));
        }
        request.setNetwork_type(0);
        if (ConnectionType.WIFI == rtbDto.getNetwork().getConnectType()) {
            request.setNetwork_type(1);
        } else if (ConnectionType.NETWORK_2G == rtbDto.getNetwork().getConnectType()) {
            request.setNetwork_type(2);
        } else if (ConnectionType.NETWORK_3G == rtbDto.getNetwork().getConnectType()) {
            request.setNetwork_type(3);
        } else if (ConnectionType.NETWORK_4G == rtbDto.getNetwork().getConnectType()) {
            request.setNetwork_type(4);
        } else if (ConnectionType.NETWORK_5G == rtbDto.getNetwork().getConnectType()) {
            request.setNetwork_type(6);
        } else if (ConnectionType.NETWORK_CELLULAR == rtbDto.getNetwork().getConnectType()) {
            request.setNetwork_type(2);
        }
        if (rtbDto.getGeo() != null) {
            request.setLatitude(rtbDto.getGeo().getLatitude());
            request.setLongitude(rtbDto.getGeo().getLongitude());
        }
        request.setSearch_word(rtbDto.getTag().getQuery());
        if (!CollectionUtils.isEmpty(rtbDto.getDevice().getInstalledAppInfo())) {
            List<String> pks = rtbDto.getDevice().getInstalledAppInfo()
                    .stream()
                    .filter(v -> StringUtils.isNotBlank(v.getPackageName()))
                    .map(v -> v.getPackageName())
                    .collect(Collectors.toList());
            request.setInstalled_pkgs(StringUtils.join(pks.toArray(new String[0]), ","));
        }
        request.setWxminiprog(1);
        advDto.setReqObj(request);
        String jsonText = JsonHelper.toJsonStringWithoutNull(request);
        HttpResult result = httpClient.postJson(advDto.getRtburl(), jsonText, "utf-8", null, advDto.getTimeout());
        RtbResponseDto fail = isHttpRequestFail(result);
        if (fail != null) {
            advDto.setRespObj(fail);
            return fail;
        }
        return parseResponse(result, rtbDto, advDto);
    }

    private RtbResponseDto parseResponse(HttpResult result, RtbRequestDto rtbDto, RtbAdvDto advDto) {
        BidResponse response = null;
        try {
            response = result.getDataObjectByJson(BidResponse.class);
        } catch (Exception E) {
            return new RtbResponseDto(LogicState.FAIL_ADV.getCode(), LogicState.FAIL_ADV.getDesc(),
                    "Serializable resp fail");
        }
        if (CollectionUtils.isEmpty(response.getAds())) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        Ads ads = response.getAds().get(0);
        if (CollectionUtils.isEmpty(ads.getCreative())) {
            return new RtbResponseDto(LogicState.SUCCESS.getCode(), "无填充");
        }
        RtbResponseDto responseDto = new RtbResponseDto(LogicState.SUCCESS.getCode(), "");
        ads.getCreative().forEach(crt -> {
            TagResponseDto tag = new TagResponseDto();
            tag.setActionType(ActionType.WEB_VIEW_H5);
            if ("DOWNLOAD".equals(crt.getInteraction_type())) {
                tag.setActionType(ActionType.DOWNLOAD);
            }
            if (crt.getInteraction_object() != null) {
                tag.setClickUrl(crt.getInteraction_object().getUrl());
                tag.setDeepLinkUrl(crt.getInteraction_object().getDeep_link());
                if (crt.getInteraction_object().getWxminiprog_info() != null) {
                    ResponseMiniProgramDto miniProgramDto = new ResponseMiniProgramDto();
                    miniProgramDto.setId(crt.getInteraction_object().getWxminiprog_info().getProg_id());
                    miniProgramDto.setPath(crt.getInteraction_object().getWxminiprog_info().getPath());
                    tag.setMiniProgram(miniProgramDto);
                }
            }
            ResponseAppDto appDto = new ResponseAppDto();
            appDto.setPackageName(crt.getPackage_name());
            if (crt.getPackage_size() != null) {
                appDto.setAppSize(crt.getPackage_size().longValue());
            }
            appDto.setAppName(crt.getApp_name());
            appDto.setAppVersion(crt.getVersion_name());
            appDto.setAppDeveloper(crt.getApp_publisher());
            appDto.setAppInfoUrl(crt.getApp_user_rights());
            appDto.setAppPrivacyUrl(crt.getApp_privacy_agreement());
            tag.setAppInfo(appDto);
            tag.setMaterialType(MaterialType.IMAGE_TEXT);
            Map<String, Object> adm = crt.getAdm();
            String naStr = JsonHelper.toJsonString(adm.get("native"));
            Navtive na = JsonHelper.fromJson(Navtive.class, naStr);
            List<String> imgUrs = new LinkedList<>();
            if (na.getImg() != null) {
                tag.setMaterialWidth(na.getImg().getWidth());
                tag.setMaterialHeight(na.getImg().getHeight());
                imgUrs.add(na.getImg().getUrl());
            }
            if (na.getLinked_img() != null) {
                imgUrs.add(na.getLinked_img().getUrl());
            }
            if (na.getMulti_imgs() != null) {
                imgUrs.addAll(na.getMulti_imgs().stream().map(Img::getUrl).collect(Collectors.toList()));
            }
            tag.setImgUrls(imgUrs);
            if (na.getTitle() != null) {
                tag.setTitle(na.getTitle().getText());
            }
            tag.setSubTitle(na.getSub_title());
            tag.setLogoUrl(na.getLogo());
            tag.setDesc(na.getDesc());
            List<ResponseTrackDto> trackDtos = new LinkedList<>();
            tag.setTracks(trackDtos);
            if (!CollectionUtils.isEmpty(crt.getEvent_track())) {
                crt.getEvent_track().forEach(e -> {
                    if ("SHOW".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.EXPOSURE.getType(), e.getNotify_url()));
                    } else if ("CLICK".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.CLICK.getType(), e.getNotify_url()));
                    } else if ("OPEN".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.INSTALLED_OPEN.getType(), e.getNotify_url()));
                    } else if ("BEGIN_DOWNLOAD".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DOWNLOAD_BEGIN.getType(), e.getNotify_url()));
                    } else if ("DOWNLOAD".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.DOWNLOAD_COMPLETED.getType(), e.getNotify_url()));
                    } else if ("INSTALL".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.INSTALL_COMPLETED.getType(), e.getNotify_url()));
                    } else if ("ACTIVE".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.ACTIVE_APP.getType(), e.getNotify_url()));
                    } else if ("CLOSE".equals(e.getEvent_type())) {
                        trackDtos.add(new ResponseTrackDto(EventType.CLOSE_AD.getType(), e.getNotify_url()));
                    }
                });
            }
            if (na.getVideo() != null) {
                tag.setMaterialType(MaterialType.VIDEO);
                ResponseVideoDto videoDto = new ResponseVideoDto();
                Video video = na.getVideo();
                videoDto.setVideoUrl(video.getUrl());
                videoDto.setDuration(video.getDuration());
                if (video.getVideo_size() != null) {
                    videoDto.setVideoSize(video.getVideo_size().longValue());
                }
                tag.setVideoInfo(videoDto);
                if (!CollectionUtils.isEmpty(video.getStart_tracks())) {
                    trackDtos.add(new ResponseTrackDto(EventType.VIDEO_BEGIN.getType(), video.getStart_tracks()));
                }
                if (!CollectionUtils.isEmpty(video.getPause_tracks())) {
                    trackDtos.add(new ResponseTrackDto(EventType.VIDEO_PAUSE.getType(), video.getPause_tracks()));
                }
                if (!CollectionUtils.isEmpty(video.getConti_tracks())) {
                    trackDtos.add(new ResponseTrackDto(EventType.VIDEO_CONTINUE.getType(), video.getConti_tracks()));
                }
                if (!CollectionUtils.isEmpty(video.getExit_tracks())) {
                    trackDtos.add(new ResponseTrackDto(EventType.VIDEO_CLOSE.getType(), video.getExit_tracks()));
                }
                if (!CollectionUtils.isEmpty(video.getComp_tracks())) {
                    trackDtos.add(new ResponseTrackDto(EventType.VIDEO_END.getType(), video.getComp_tracks()));
                }
                if (!CollectionUtils.isEmpty(video.getTShowTrack())) {
                    video.getTShowTrack().forEach(tt -> {
                        if (tt.getT() == video.getDuration() * 0.25) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_25.getType(), tt.getUrl()));
                        } else if (tt.getT() == video.getDuration() * 0.5) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_50.getType(), tt.getUrl()));
                        } else if (tt.getT() == video.getDuration() * 0.75) {
                            trackDtos.add(new ResponseTrackDto(EventType.VIDEO_75.getType(), tt.getUrl()));
                        }
                    });
                }
            }
            tag.setWinNoticeUrls(new ArrayList<>());
            tag.setFailNoticeUrls(new ArrayList<>());

            // 宏替换
            tag.getTracks().forEach(track -> {
                List<String> urls = track.getTrackUrls();
                urls = replaceMacro("__COMPONENT_TYPE__", urls, "1");
                urls = replaceMacro("__EVENT_TIME_START__", urls, MacroType.START_TIME.getCode());
                urls = replaceMacro("__EVENT_TIME_END__", urls, MacroType.END_TIME.getCode());
                urls = replaceMacro("__OFFSET_X__", urls, MacroType.UP_X.getCode());
                urls = replaceMacro("__OFFSET_Y__", urls, MacroType.UP_Y.getCode());
                track.setTrackUrls(urls);
            });
            if (StringUtils.isNotBlank(tag.getClickUrl())) {
                String temUrl = tag.getClickUrl();
                temUrl = temUrl.replace("__EVENT_TIME_START__", MacroType.START_TIME.getCode());
                temUrl = temUrl.replace("__EVENT_TIME_END__", MacroType.END_TIME.getCode());
                temUrl = temUrl.replace("__OFFSET_X__", MacroType.UP_X.getCode());
                temUrl = temUrl.replace("__OFFSET_Y__", MacroType.UP_Y.getCode());
                tag.setClickUrl(temUrl);
            }
            responseDto.getTags().add(tag);
        });

        return responseDto;
    }

    private List<Adspace> createAdspaces(RtbRequestDto rtbDto, RtbAdvDto advDto) {
        Adspace adspace = new Adspace();
        RequestTagDto tagDto = rtbDto.getTag();
        adspace.setAdspace_id(advDto.getTagCode());
        adspace.setAdspace_type(4);
        adspace.setAdspace_position(0);
        if (TagType.BANNER == tagDto.getTagType()) {
            adspace.setAdspace_type(1);
        } else if (TagType.INTERSTITIAL == tagDto.getTagType()) {
            adspace.setAdspace_type(2);
        } else if (TagType.NORMAL_VIDEO == tagDto.getTagType() || TagType.INCENTIVE_VIDEO == tagDto.getTagType()) {
            adspace.setAdspace_type(3);
        } else if (TagType.NATIVE == tagDto.getTagType()) {
            adspace.setAdspace_type(4);
        } else if (TagType.OPEN == tagDto.getTagType()) {
            adspace.setAdspace_type(6);
        }
        adspace.setAllowed_html(false);
        adspace.setWidth(tagDto.getWidth());
        adspace.setHeight(tagDto.getHeight());
        adspace.setKeywords(tagDto.getQuery());
        return ListUtils.asList(adspace);
    }

    private void createIds(List<DeviceId> list, String did, int type, int md5, boolean lowerCase) {
        if (StringUtils.isBlank(did)) {
            return;
        }
        did = lowerCase ? did.toLowerCase() : did;
        DeviceId devId = new DeviceId();
        devId.setDevice_id(did);
        devId.setDevice_id_type(type);
        devId.setHash_type(md5);
        list.add(devId);
    }

    private Device createDevice(RtbRequestDto rtbDto, RequestNetworkDto networkDto) {
        RequestDeviceDto deviceDto = rtbDto.getDevice();
        Device device = new Device();
        List<DeviceId> ids = new LinkedList<>();
        createIds(ids, deviceDto.getImei(), 1, 0, true);
        createIds(ids, deviceDto.getImeiMd5(), 1, 1, true);
        createIds(ids, deviceDto.getIdfa(), 2, 0, true);
        createIds(ids, deviceDto.getIdfaMd5(), 2, 1, true);
        createIds(ids, deviceDto.getAndroidId(), 3, 0, true);
        createIds(ids, deviceDto.getAndroidIdMd5(), 3, 1, true);
        createIds(ids, networkDto.getMac(), 4, 0, true);
        createIds(ids, networkDto.getMacMd5(), 4, 1, true);
        createIds(ids, deviceDto.getIdfv(), 5, 0, true);
        createIds(ids, deviceDto.getIdfvMd5(), 5, 1, true);
        createIds(ids, deviceDto.getImsi(), 8, 0, false);
        createIds(ids, deviceDto.getImsiMd5(), 8, 1, false);
        createIds(ids, deviceDto.getOaid(), 9, 0, false);
        createIds(ids, deviceDto.getOaidMd5(), 9, 1, false);
        device.setDevice_id(ids);
        if (OsType.IOS == deviceDto.getOsType()) {
            device.setOs_type(1);
        } else {
            device.setOs_type(2);
        }
        device.setOs_version(deviceDto.getOsVersion());
        device.setBrand(deviceDto.getBrand());
        device.setModel(deviceDto.getModel());
        device.setDevice_type(0);
        if (DeviceType.PHONE == deviceDto.getDeviceType()) {
            device.setDevice_type(2);
        } else if (DeviceType.PAD == deviceDto.getDeviceType()) {
            device.setDevice_type(1);
        }
        device.setBoot_mark(deviceDto.getBootMark());
        device.setUpdate_mark(deviceDto.getUpdateMark());
        device.setScreen_width(deviceDto.getWidth());
        device.setScreen_height(deviceDto.getHeight());
        device.setScreen_density(deviceDto.getScreenDensity());
        device.setScreen_orientation(0);
        if (OrientationType.VERTICAL == deviceDto.getOrientation()) {
            device.setScreen_orientation(1);
        } else if (OrientationType.HORIZONTAL == deviceDto.getOrientation()) {
            device.setScreen_orientation(2);
        }
        CarrierType carrierType = rtbDto.getNetwork().getCarrierType();
        if (CarrierType.CM == carrierType) {
            device.setCarrier_id(70120);
        } else if (CarrierType.CT == carrierType) {
            device.setCarrier_id(70121);
        } else if (CarrierType.CU == carrierType) {
            device.setCarrier_id(70123);
        } else {
            device.setCarrier_id(0);
        }
        device.setBattery_level(deviceDto.getBatteryPower());
        return device;
    }

    private App createApp(RtbRequestDto rtbDto) {
        if (rtbDto == null || rtbDto.getApp() == null) {
            return null;
        }
        RequestAppDto requestAppDto = rtbDto.getApp();
        App app = new App();
        app.setApp_name(requestAppDto.getAppName());
        app.setPackage_name(requestAppDto.getBundle());
        app.setApp_version(requestAppDto.getAppVersion());
        return app;
    }

    @Override
    public SuperResult<String> billResult(FastHttpClient httpClient, RtbReqAdvBillParam param) throws Exception {
        return SuperResult.rightResult();
    }
}
