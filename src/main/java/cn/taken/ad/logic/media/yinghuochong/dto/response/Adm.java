package cn.taken.ad.logic.media.yinghuochong.dto.response;

import cn.taken.ad.logic.media.yinghuochong.dto.request.YingHuoChongActionType;
import cn.taken.ad.logic.media.yinghuochong.dto.request.YingHuoChongAdType;

import java.util.List;
import java.util.Map;

public class Adm {
    private Integer adType;
    private Integer actionType;
    private String title;
    private String subTitle;
    private String desc;
    private String icon;
    private List<Image> imgInfo;
    private Video videoInfo;
    private Map<String,Object> androidApp;
    private LandingPage landingPage;
    private String deeplink;
    private String quickAppLink;
    private Integer materialType;
    private Integer ratio;

    public Integer getAdType() {
        return adType;
    }

    public void setAdType(Integer adType) {
        this.adType = adType;
    }

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<Image> getImgInfo() {
        return imgInfo;
    }

    public void setImgInfo(List<Image> imgInfo) {
        this.imgInfo = imgInfo;
    }

    public Video getVideoInfo() {
        return videoInfo;
    }

    public void setVideoInfo(Video videoInfo) {
        this.videoInfo = videoInfo;
    }

    public Map<String, Object> getAndroidApp() {
        return androidApp;
    }

    public void setAndroidApp(Map<String, Object> androidApp) {
        this.androidApp = androidApp;
    }

    public LandingPage getLandingPage() {
        return landingPage;
    }

    public void setLandingPage(LandingPage landingPage) {
        this.landingPage = landingPage;
    }

    public String getDeeplink() {
        return deeplink;
    }

    public void setDeeplink(String deeplink) {
        this.deeplink = deeplink;
    }

    public String getQuickAppLink() {
        return quickAppLink;
    }

    public void setQuickAppLink(String quickAppLink) {
        this.quickAppLink = quickAppLink;
    }

    public Integer getMaterialType() {
        return materialType;
    }

    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }

    public Integer getRatio() {
        return ratio;
    }

    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }
}
