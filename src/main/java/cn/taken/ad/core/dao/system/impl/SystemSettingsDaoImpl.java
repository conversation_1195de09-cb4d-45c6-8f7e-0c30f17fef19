package cn.taken.ad.core.dao.system.impl;

import cn.taken.ad.configuration.database.BasePojoSuperDaoImpl;
import cn.taken.ad.core.dao.system.SystemSettingsDao;
import cn.taken.ad.core.pojo.system.SystemSettings;
import org.springframework.stereotype.Repository;

/**
 *
 */
@Repository
public class SystemSettingsDaoImpl extends BasePojoSuperDaoImpl<SystemSettings> implements SystemSettingsDao {


    @Override
    public void update(String key, String value) {
        String sql = "update system_settings set setting_value = ? where setting_key = ?";
        this.getJdbcTemplate().update(sql, value, key);
    }


}