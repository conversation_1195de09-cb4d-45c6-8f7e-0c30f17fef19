package cn.taken.ad.core.service.system.impl;

import cn.taken.ad.core.dao.system.SystemSettingsDao;
import cn.taken.ad.core.pojo.system.SystemSettings;
import cn.taken.ad.core.service.system.SystemSettingsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * auth
 */
@Service
public class SystemSettingsServiceImpl implements SystemSettingsService {

    @Resource
    private SystemSettingsDao systemSettingsDao;


    @Override
    public void save(SystemSettings settings) {
        systemSettingsDao.save(settings);
    }

    @Override
    public SystemSettings findById(Long id) {
        return systemSettingsDao.findById(id);
    }

    @Override
    public void update(String key,String value){
        systemSettingsDao.update(key,value);
    }

    @Override
    public List<SystemSettings> findAll() {
        return systemSettingsDao.findAll();
    }
}