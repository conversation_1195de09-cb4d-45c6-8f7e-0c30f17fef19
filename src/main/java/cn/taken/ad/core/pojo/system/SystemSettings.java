package cn.taken.ad.core.pojo.system;

import javax.persistence.*;

/**
 * 系统参数
 *
 *  c
 */
@Entity
@Table(name = "system_settings")
public class SystemSettings implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 黑域名
     */
    public static final String BLACK_DOMAIN_LIST = "blackDomainList";

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 参数key
     */
    @Column(name = "setting_key")
    private String settingKey;
    /**
     * 参数value
     */
    @Column(name = "setting_value")
    private String settingValue;
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
    /**
     * 发起任务的用户ID
     */
    @Column(name = "oper_user_id")
    private Long operUserId;

    public SystemSettings() {

    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSettingKey() {
        return settingKey;
    }

    public void setSettingKey(String settingKey) {
        this.settingKey = settingKey;
    }

    public String getSettingValue() {
        return settingValue;
    }

    public void setSettingValue(String settingValue) {
        this.settingValue = settingValue;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getOperUserId() {
        return operUserId;
    }

    public void setOperUserId(Long operUserId) {
        this.operUserId = operUserId;
    }
}
