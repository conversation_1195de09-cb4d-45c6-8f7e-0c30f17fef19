import Vue from 'vue'
import Router from 'vue-router'
import Layout from '@/components/layout/index.vue'

Vue.use(Router)

/**
   hidden: true                   当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1,默认false
   redirect: noRedirect           当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
                                  设置直接跳转到另外的URL
   name:'router-name'             设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
   meta : {
    title: 'title'                设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'              设置该路由的图标
    noCache: true                 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    affix: true                   在tags-view中永久显示，不能被关闭
    auth:                         路由需要的权限，如果不设置则不需要权限
  }
 */

/**
 * 没有权限要求的基本页
 * 所有用户都可以访问
 */
const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login'),
    hidden: true
  },
  {
    path: '/401',
    name: 'ErrorOut',
    component: () => import('@/views/401'),
    hidden: true,
    meta: { title: '错误页', icon: 'error', noCache: true }
  },
  {
    path: '/',
    component: Layout,
    name: 'Root',
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard'),
        meta: { title: '首页', icon: 'dashboard', affix: true }
      },
      {
        path: '/404',
        name: 'Error',
        component: () => import('@/views/404'),
        hidden: true,
        meta: { title: '错误页', icon: 'error', noCache: true }
      },
      {
        path: '/changepass',
        component: () => import('@/views/ChangePass'),
        name: 'Changepass',
        hidden: true,
        meta: { title: '修改密码', icon: 'user', noCache: true }
      },
      {
        path: '/redirect/:path*',
        component: () => import('@/views/Redirect'),
        hidden: true,
        name: 'Redirect',
        meta: { title: '中转页', icon: 'redirect', noCache: true }
      }
    ]
  }
]

/**
 * 基础数据
 */
const baseDataRoutes = [
  {
    path: '/base',
    component: Layout,
    redirect: '/404',
    meta: { title: '基础', icon: 'size' },
    children: [
      {
        path: '/base/industry',
        component: () => import('@/views/base/industry/AppIndustry'),
        name: 'AppIndustry',
        meta: { title: '行业', icon: 'education', auth: 'BASE_APP_INDUSTRY_VIEW' }
      },
      {
        path: '/base/area/index',
        component: () => import('@/views/base/area/AreaIndex'),
        name: 'AreaIndex',
        meta: { title: '地域', icon: 'example', auth: 'BASE_AREA_VIEW' }
      },
      {
        path: '/base/errCode',
        component: () => import('@/views/base/errcode/BaseErrorCode'),
        name: 'BaseErrorCode',
        meta: { title: '错误码', icon: 'deletefile', auth: 'BASE_ERROR_CODE_VIEW' }
      }
    ]
  }
]

/**
 * 工具
 */
const toolRoutes = [
  {
    path: '/tool',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '工具', icon: 'fullscreen' },
    children: [
      {
        path: '/tool/maintain/index',
        component: () => import('@/views/tool/maintain/ToolMaintainIndex'),
        name: 'ToolMaintainIndex',
        meta: { title: '运维工具', icon: 'lock', auth: 'TOOL_MAINTAIN_VIEW' }
      },
      {
        path: '/tool/rtb/rtbLog',
        component: () => import('@/views/tool/rtb/RtbLog'),
        name: 'RtbLog',
        meta: { title: 'Rtb日志', icon: 'parameter', auth: 'TOOL_RTB_LOG' }
      },
      {
        path: '/tool/error/main',
        component: () => import('@/views/tool/error/ErrorCode'),
        name: 'ErrorCode',
        meta: { title: '错误码日志', icon: 'deletefile', auth: 'TOOL_ERROR_CODE_LOG' }
      },
      {
        path: '/tool/conversion/main',
        component: () => import('@/views/tool/conversion/ConversionTools'),
        name: 'ConversionTools',
        meta: { title: '转链工具', icon: 'link', auth: 'TOOL_CONVERSION_VIEW' }
      }
    ]
  }
]

/**
 * 系统
 */
const systemRoutes = [
  {
    path: '/system',
    component: Layout,
    redirect: '/404',
    meta: { title: '系统', icon: 'nested' },
    children: [
      {
        path: '/system/user/index',
        component: () => import('@/views/system/user/User'),
        name: 'User',
        meta: { title: '用户管理', icon: 'user', auth: 'MANAGE_USER_VIEW' }
      },
      {
        path: '/system/role/index',
        component: () => import('@/views/system/role/Role'),
        name: 'Role',
        meta: { title: '角色管理', icon: 'people', auth: 'MANAGE_ROLE_VIEW' }
      },
      {
        path: '/system/log/index',
        component: () => import('@/views/system/log/Log'),
        name: 'Log',
        meta: { title: '操作日志', icon: 'excel', auth: 'OPER_LOG_VIEW' }
      },
      {
        path: '/system/task/index',
        component: () => import('@/views/system/task/SystemTask'),
        name: 'SystemTask',
        meta: { title: '系统任务', icon: 'list', auth: 'SYSTEM_TASK_VIEW' }
      },
      {
        path: '/system/settings/index',
        component: () => import('@/views/system/settings/SettingsManage'),
        name: 'SettingsManage',
        meta: { title: '系统设置', icon: 'list', auth: 'SYSTEM_SETTINGS_VIEW' }
      }
    ]
  }
]

/**
 * 媒体
 */
const mediaRoutes = [
  {
    path: '/media',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '媒体', icon: 'international' },
    children: [
      {
        path: '/media/protocol',
        component: () => import('@/views/media/protocol/MediaProtocol'),
        name: 'MediaProtocol',
        meta: { title: '媒体协议', icon: 'email', auth: 'MEDIA_PROTOCOL_VIVE' }
      },
      {
        path: '/media/main',
        component: () => import('@/views/media/main/Media'),
        name: 'Media',
        meta: { title: '媒体管理', icon: 'eye-open', auth: 'MEDIA_VIEW' }
      },
      {
        path: '/media/app',
        component: () => import('@/views/media/app/MediaApp'),
        name: 'MediaApp',
        meta: { title: '媒体APP', icon: 'star', auth: 'MEDIA_APP_VIEW' }
      },
      {
        path: '/media/tag',
        component: () => import('@/views/media/tag/MediaTag'),
        name: 'MediaTag',
        meta: { title: '媒体广告位', icon: 'list', auth: 'MEDIA_TAG_VIEW' }
      },
      {
        path: '/media/user',
        component: () => import('@/views/media/user/MediaUser'),
        name: 'MediaUser',
        meta: { title: '媒体用户', icon: 'peoples', auth: 'MEDIA_USER_VIEW' }
      }
    ]
  }
]

/**
 * 预算
 */
const advertiserRoutes = [
  {
    path: '/advertiser',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '预算', icon: 'customer' },
    children: [
      {
        path: '/advertiser/protocol',
        component: () => import('@/views/advertiser/protocol/AdvertiserProtocol'),
        name: 'AdvertiserProtocol',
        meta: { title: '预算协议', icon: 'email', auth: 'ADVERT_PROTOCOL_VIEW' }
      },
      {
        path: '/advertiser/main',
        component: () => import('@/views/advertiser/main/Advertiser'),
        name: 'Advertiser',
        meta: { title: '预算管理', icon: 'eye-open', auth: 'ADVERT_VIEW' }
      },
      {
        path: '/advertiser/app',
        component: () => import('@/views/advertiser/app/AdvertiserApp'),
        name: 'AdvertiserApp',
        meta: { title: '预算APP', icon: 'star', auth: 'ADVERT_APP_VIEW' }
      },
      {
        path: '/advertiser/tag',
        component: () => import('@/views/advertiser/tag/AdvertiserTag'),
        name: 'AdvertiserTag',
        meta: { title: '预算广告位', icon: 'list', auth: 'ADVERT_TAG_VIEW' }
      }
    ]
  }
]

/**
 * 策略
 */
const strategyRoutes = [
  {
    path: '/strategy',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '策略', icon: 'drag' },
    children: [
      {
        path: '/strategy/main',
        component: () => import('@/views/strategy/main/Strategy'),
        name: 'Strategy',
        meta: { title: '策略管理', icon: 'guide', auth: 'STRATEGY_VIEW' }
      },
      {
        path: '/strategy/config',
        component: () => import('@/views/strategy/rule/StrategyRuleConfig'),
        name: 'StrategyRuleConfig',
        meta: { title: '策略规则', icon: 'guide', auth: 'STRATEGY_VIEW' }
      }
    ]
  }
]

const statisticsRoutes = [
  {
    path: '/statistics',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '统计', icon: 'statistical' },
    children: [
      {
        path: '/statistics/media',
        component: () => import('@/views/statistics/media/StatisticsMediaRequest'),
        name: 'StatisticsMediaRequest',
        meta: { title: '媒体统计', icon: 'parameter', auth: 'STATISTICS_MEDIA_VIEW' }
      },
      {
        path: '/statistics/advertiser',
        component: () => import('@/views/statistics/advertiser/StatisticsAdvertiserRequest'),
        name: 'StatisticsAdvertiserRequest',
        meta: { title: '预算统计', icon: 'parameter', auth: 'STATISTICS_ADVERT_VIEW' }
      },
      {
        path: '/statistics/mediaAdv',
        component: () => import('@/views/statistics/mediaAdv/StatisticsMediaAdvRequest'),
        name: 'StatisticsMediaAdvRequest',
        meta: { title: '综合统计', icon: 'parameter', auth: 'STATISTICS_MEDIA_ADVERT_VIEW' }
      }
    ]
  }
]
const financialRoutes = [
  {
    path: '/financial',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '账务', icon: 'reported' },
    children: [
      {
        path: '/financial/media',
        component: () => import('@/views/financial/media/FinancialMedia'),
        name: 'FinancialMedia',
        meta: { title: '媒体账务', icon: 'parameter', auth: 'FINANCIAL_MEDIA_VIEW' }
      },
      {
        path: '/financial/advertiser',
        component: () => import('@/views/financial/advertiser/FinancialAdvertiser'),
        name: 'FinancialAdvertiser',
        meta: { title: '预算账务', icon: 'parameter', auth: 'FINANCIAL_ADVERTISER_VIEW' }
      },
      {
        path: '/financial/mediaAdv',
        component: () => import('@/views/financial/mediaAdv/FinancialMediaAdvertiser'),
        name: 'FinancialMediaAdvertiser',
        meta: { title: '综合账务', icon: 'parameter', auth: 'FINANCIAL_MEDIA_ADVERTISER_VIEW' }
      }
    ]
  }
]

const dspRoutes = [
  {
    path: '/dsp',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: 'DSP', icon: 'shopping' },
    children: [
      {
        path: '/dsp/app',
        component: () => import('@/views/dsp/app/AdvertiserApp'),
        name: 'DspAdvertiserApp',
        meta: { title: 'APP管理', icon: 'star', auth: 'DSP_ADVERT_APP_VIEW' }
      },
      {
        path: '/dsp/account',
        component: () => import('@/views/dsp/account/Account'),
        name: 'DspAccount',
        meta: { title: '广告账户', icon: 'education', auth: 'DSP_ADVERT_ACCOUNT_VIEW' }
      },
      {
        path: '/dsp/ad/',
        component: () => import('@/views/dsp/ad/Ad'),
        name: 'DspAdvertiserAd',
        meta: { title: '广告创意', icon: 'theme', auth: 'DSP_ADVERT_AD_VIEW' }
      },
      {
        path: '/dsp/ad/statistics',
        component: () => import('@/views/dsp/statistics/StatisticsDspAd'),
        name: 'StatisticsDspAd',
        meta: { title: '广告统计', icon: 'statistical', auth: 'STATISTICS_DSP_AD_VIEW' }
      }
    ]
  }
]

const dmpRoutes = [
  {
    path: '/dmp',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: 'DMP', icon: 'star' },
    children: [
      {
        path: '/dmp/pack',
        component: () => import('@/views/dmp/pack/DmpPackage'),
        name: 'DmpPackage',
        meta: { title: '人群包', icon: 'user', auth: 'DMP_PACK_VIEW' }
      }
    ]
  }
]

const visitorRoutes = [
  {
    path: '/visitor',
    component: Layout,
    redirect: '/404',
    alwaysShow: true,
    meta: { title: '合作伙伴', icon: 'star' },
    children: [
      {
        path: '/visitor/user',
        component: () => import('@/views/collab/visitor/User'),
        name: 'VisitorUser',
        meta: { title: '用户管理', icon: 'user', auth: 'VISITOR_USER_VIEW' }
      }
    ]
  }
]

const allRoutes = constantRoutes.concat(mediaRoutes, advertiserRoutes, strategyRoutes, statisticsRoutes, financialRoutes, dspRoutes, dmpRoutes, baseDataRoutes, toolRoutes, systemRoutes, visitorRoutes)

const router = new Router({
  scrollBehavior: () => ({ y: 0 }),
  routes: allRoutes
})

export default router
