[2025-07-07 20:01:47.818] [cn.taken.ad.WebApplication.logStarting,50] INFO  - Starting WebApplication on chang with PID 44836 (D:\ideaProjects\ssc\ssp-web-service\target\classes started by chang in D:\ideaProjects\ssc)
[2025-07-07 20:01:47.828] [cn.taken.ad.WebApplication.logStartupProfileInfo,652] INFO  - The following profiles are active: dev
[2025-07-07 20:01:49.534] [org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn,127] INFO  - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2025-07-07 20:01:50.173] [org.springframework.data.repository.config.RepositoryConfigurationDelegate.registerRepositoriesIn,185] INFO  - Finished Spring Data repository scanning in 620ms. Found 0 JPA repository interfaces.
[2025-07-07 20:01:51.362] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize,90] INFO  - <PERSON><PERSON> initialized with port(s): 8083 (http)
[2025-07-07 20:01:51.386] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Initializing ProtocolHandler ["http-nio-8083"]
[2025-07-07 20:01:51.398] [org.apache.catalina.core.StandardService.log,173] INFO  - Starting service [Tomcat]
[2025-07-07 20:01:51.398] [org.apache.catalina.core.StandardEngine.log,173] INFO  - Starting Servlet engine: [Apache Tomcat/9.0.36]
[2025-07-07 20:01:51.571] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/web].log,173] INFO  - Initializing Spring embedded WebApplicationContext
[2025-07-07 20:01:51.572] [org.springframework.web.context.ContextLoader.prepareWebApplicationContext,284] INFO  - Root WebApplicationContext: initialization completed in 3696 ms
[2025-07-07 20:01:51.989] [org.hibernate.Version.logVersion,46] INFO  - HHH000412: Hibernate Core {5.3.17.Final}
[2025-07-07 20:01:51.993] [org.hibernate.cfg.Environment.<clinit>,213] INFO  - HHH000206: hibernate.properties not found
[2025-07-07 20:01:52.713] [org.hibernate.annotations.common.Version.<clinit>,49] INFO  - HCANN000001: Hibernate Commons Annotations {5.0.4.Final}
[2025-07-07 20:01:54.105] [org.hibernate.dialect.Dialect.<init>,157] INFO  - HHH000400: Using dialect: org.hibernate.dialect.MySQLInnoDBDialect
[2025-07-07 20:02:00.057] [cn.taken.ad.configuration.server.ServerInfoManager.getNew,56] INFO  - new service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:02:05.847] [org.apache.coyote.http11.Http11NioProtocol.log,173] INFO  - Starting ProtocolHandler ["http-nio-8083"]
[2025-07-07 20:02:05.920] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start,202] INFO  - Tomcat started on port(s): 8083 (http) with context path '/web'
[2025-07-07 20:02:05.929] [cn.taken.ad.WebApplication.logStarted,59] INFO  - Started WebApplication in 18.776 seconds (JVM running for 20.124)
[2025-07-07 20:02:35.921] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:03:06.009] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:03:36.092] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:03:38.607] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/web].log,173] INFO  - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-07-07 20:03:38.607] [org.springframework.web.servlet.DispatcherServlet.initServletBean,525] INFO  - Initializing Servlet 'dispatcherServlet'
[2025-07-07 20:03:38.632] [org.springframework.web.servlet.DispatcherServlet.initServletBean,547] INFO  - Completed initialization in 25 ms
[2025-07-07 20:03:39.858] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[unknown] uri[/web/o/pub/loginCaptcha/dec9b3be-938b-4ce7-aa32-34c8972d7d76/1751889818990] stack[cn.taken.ad.api.oper.pub.OperPublicApi.loginCaptcha] time[2025-07-07 20:03:39 to 2025-07-07 20:03:39] params[uuid="dec9b3be-938b-4ce7-aa32-34c8972d7d76" ; flush="1751889818990" ; ] response[]  SUCCESS
[2025-07-07 20:03:43.959] [org.hibernate.hql.internal.QueryTranslatorFactoryInitiator.initiateService,47] INFO  - HHH000397: Using ASTQueryTranslatorFactory
[2025-07-07 20:03:45.613] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[unknown] uri[/web/o/pub/login] stack[cn.taken.ad.api.oper.pub.OperPublicApi.login] time[2025-07-07 20:03:43 to 2025-07-07 20:03:45] params[loginReq={"username":"changyw","password":"b39983ff2eae704638b3452c2f07dc33","captcha":"9304","uuid":"dec9b3be-938b-4ce7-aa32-34c8972d7d76"} ; ] response[{"success":true,"code":"0","message":"success","result":{"sessionId":"9363603485FB484CAC92EDBD11375A0C","realname":"cyw","userId":16,"username":"changyw","resources":{"STRATEGY_ADD":true,"STATISTICS_MEDIA_ADVERT_EXPORT":true,"MEDIA_TAG_MODIFY":true,"SYSTEM_TASK_VIEW":true,"MEDIA_MODIFY":true,"STATISTICS_MEDIA_VIEW":true,"TOOL_CONVERSION_IMPORT":true,"ADVERT_PROTOCOL_MODIFY":true,"BASE_APP_INDUSTRY_ADD":true,"MEDIA_VIEW":true,"MANAGE_ROLE_VIEW":true,"ADVERT_PROTOCOL_VIEW":true,"MANAGE_ROLE_ADD":true,"STATISTICS_MEDIA_ADVERT_VIEW":true,"STRATEGY_VIEW":true,"FINANCIAL_MEDIA_ADVERTISER_IMPORT":true,"DSP_ADVERT_AD_MODIFY":true,"STATISTICS_ADVERT_EXPORT":true,"BASE_ERROR_CODE_MODIFY":true,"ADVERT_APP_MODIFY":true,"ADVERT_PROTOCOL_ADD":true,"BASE_ERROR_CODE_ADD":true,"MEDIA_TAG_VIEW":true,"MEDIA_PROTOCOL_ADD":true,"OPER_LOG_VIEW":true,"MEDIA_APP_VIEW":true,"MANAGE_USER_OPER":true,"MEDIA_ADD":true,"VISITOR_USER_MODIFY":true,"DSP_ADVERT_APP_MODIFY":true,"MEDIA_PROTOCOL_VIVE":true,"TOOL_CONVERSION_VIEW":true,"ADVERT_TAG_ADD":true,"MEDIA_APP_AUDIT":true,"MANAGE_USER_MODIFY":true,"ADVERT_APP_ADD":true,"MANAGE_USER_DELETE":true,"DMP_PACK_MODIFY":true,"STRATEGY_MODIFY":true,"STRATEGY_OPERATOR":true,"DSP_ADVERT_AD_ADD":true,"FINANCIAL_MEDIA_VIEW":true,"ADVERT_APP_VIEW":true,"MEDIA_USER_ADD":true,"MANAGE_USER_ADD":true,"DSP_ADVERT_APP_ADD":true,"ADVERT_TAG_VIEW":true,"MEDIA_USER_MODIFY":true,"DMP_PACK_ADD":true,"DSP_ADVERT_ACCOUNT_DELETE":true,"TOOL_CONVERSION_ADD":true,"MEDIA_USER_VIEW":true,"FINANCIAL_MEDIA_EXPORT":true,"DSP_ADVERT_ACCOUNT_MODIFY":true,"FINANCIAL_MEDIA_ADVERTISER_VIEW":true,"DSP_ADVERT_AD_VIEW":true,"FINANCIAL_ADVERTISER_IMPORT":true,"ADVERT_VIEW":true,"STRATEGY_RULE_OPERATOR":true,"FINANCIAL_MEDIA_IMPORT":true,"MANAGE_USER_VIEW":true,"DMP_PACK_VIEW":true,"BASE_APP_INDUSTRY_VIEW":true,"VISITOR_USER_VIEW":true,"DSP_ADVERT_APP_VIEW":true,"STATISTICS_MEDIA_EXPORT":true,"FINANCIAL_ADVERTISER_MODIFY":true,"ADVERT_ADD":true,"FINANCIAL_MEDIA_MODIFY":true,"VISITOR_USER_ADD":true,"STATISTICS_DSP_AD_EXPORT":true,"DSP_ADVERT_AD_DELETE":true,"TOOL_RTB_LOG":true,"ADVERT_MODIFY":true,"MEDIA_TAG_ADD":true,"STRATEGY_RULE_MODIFY":true,"VISITOR_USER_DELETE":true,"MEDIA_APP_ADD":true,"STATISTICS_ADVERT_VIEW":true,"MEDIA_USER_REMOVE":true,"FINANCIAL_ADVERTISER_VIEW":true,"BASE_AREA_MODIFY":true,"DMP_PACK_OPERATOR":true,"TOOL_MAINTAIN_SERVICE_VIEW":true,"FINANCIAL_MEDIA_ADVERTISER_MODIFY":true,"TOOL_MAINTAIN_CACHE_OPER":true,"TOOL_CONVERSION_EXPORT":true,"ADVERT_TAG_MODIFY":true,"FINANCIAL_ADVERTISER_EXPORT":true,"BASE_AREA_ADD":true,"DSP_ADVERT_ACCOUNT_VIEW":true,"MEDIA_PROTOCOL_MODIFY":true,"VISITOR_USER_OPER":true,"TOOL_ERROR_CODE_LOG":true,"MEDIA_APP_MODIFY":true,"BASE_AREA_VIEW":true,"TOOL_MAINTAIN_VIEW":true,"BASE_APP_INDUSTRY_MODIFY":true,"MANAGE_ROLE_MODIFY":true,"BASE_ERROR_CODE_VIEW":true,"TOOL_CONVERSION_MODIFY":true,"DSP_ADVERT_ACCOUNT_ADD":true,"STRATEGY_RULE_ADD":true,"FINANCIAL_MEDIA_ADVERTISER_EXPORT":true,"MANAGE_ROLE_DELETE":true,"STATISTICS_DSP_AD_VIEW":true,"STRATEGY_RULE_VIEW":true},"lastChangePasswordTime":"2025-04-27 14:22:22","timeout":*************}}]  SUCCESS
[2025-07-07 20:03:46.570] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:03:46 to 2025-07-07 20:03:46] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:04","19:05","19:06","19:07","19:08","19:09","19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:03:46.790] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:03:46 to 2025-07-07 20:03:46] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:04:06.178] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:04:36.262] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:04:46.005] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:04:45 to 2025-07-07 20:04:46] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:04","19:05","19:06","19:07","19:08","19:09","19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:04:46.015] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:04:46 to 2025-07-07 20:04:46] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:05:06.340] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:05:36.416] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:05:46.250] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:05:46 to 2025-07-07 20:05:46] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:05:46.251] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:05:46 to 2025-07-07 20:05:46] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:06","19:07","19:08","19:09","19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03","20:04","20:05"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:06:06.495] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:06:36.585] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:06:45.991] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:06:45 to 2025-07-07 20:06:45] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:06","19:07","19:08","19:09","19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03","20:04","20:05"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:06:46.011] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:06:46 to 2025-07-07 20:06:46] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:07:06.664] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:07:36.744] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:07:46.227] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:07:45 to 2025-07-07 20:07:46] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:08","19:09","19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03","20:04","20:05","20:06","20:07"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:07:46.241] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:07:46 to 2025-07-07 20:07:46] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:08:06.837] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:08:36.919] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:08:45.999] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:08:45 to 2025-07-07 20:08:45] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:08","19:09","19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03","20:04","20:05","20:06","20:07"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:08:46.020] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:08:46 to 2025-07-07 20:08:46] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:09:07.003] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:09:20.869] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[unknown] uri[/web/o/pub/logout] stack[cn.taken.ad.api.oper.pub.OperPublicApi.logout] time[2025-07-07 20:09:20 to 2025-07-07 20:09:20] params[] response[{"success":true,"code":"0","message":"success","result":null}]  SUCCESS
[2025-07-07 20:09:20.971] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[unknown] uri[/web/o/pub/loginCaptcha/dec9b3be-938b-4ce7-aa32-34c8972d7d76/1751890160883] stack[cn.taken.ad.api.oper.pub.OperPublicApi.loginCaptcha] time[2025-07-07 20:09:20 to 2025-07-07 20:09:20] params[uuid="dec9b3be-938b-4ce7-aa32-34c8972d7d76" ; flush="1751890160883" ; ] response[]  SUCCESS
[2025-07-07 20:09:26.881] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[unknown] uri[/web/o/pub/login] stack[cn.taken.ad.api.oper.pub.OperPublicApi.login] time[2025-07-07 20:09:26 to 2025-07-07 20:09:26] params[loginReq={"username":"changyw","password":"b39983ff2eae704638b3452c2f07dc33","captcha":"5299","uuid":"dec9b3be-938b-4ce7-aa32-34c8972d7d76"} ; ] response[{"success":true,"code":"0","message":"success","result":{"sessionId":"0345BBC461C64F0A91BA3C27CC60ECFD","realname":"cyw","userId":16,"username":"changyw","resources":{"STRATEGY_ADD":true,"STATISTICS_MEDIA_ADVERT_EXPORT":true,"MEDIA_TAG_MODIFY":true,"SYSTEM_TASK_VIEW":true,"MEDIA_MODIFY":true,"STATISTICS_MEDIA_VIEW":true,"TOOL_CONVERSION_IMPORT":true,"ADVERT_PROTOCOL_MODIFY":true,"BASE_APP_INDUSTRY_ADD":true,"MEDIA_VIEW":true,"MANAGE_ROLE_VIEW":true,"ADVERT_PROTOCOL_VIEW":true,"MANAGE_ROLE_ADD":true,"STATISTICS_MEDIA_ADVERT_VIEW":true,"STRATEGY_VIEW":true,"FINANCIAL_MEDIA_ADVERTISER_IMPORT":true,"DSP_ADVERT_AD_MODIFY":true,"STATISTICS_ADVERT_EXPORT":true,"BASE_ERROR_CODE_MODIFY":true,"ADVERT_APP_MODIFY":true,"ADVERT_PROTOCOL_ADD":true,"BASE_ERROR_CODE_ADD":true,"MEDIA_TAG_VIEW":true,"MEDIA_PROTOCOL_ADD":true,"OPER_LOG_VIEW":true,"MEDIA_APP_VIEW":true,"MANAGE_USER_OPER":true,"MEDIA_ADD":true,"VISITOR_USER_MODIFY":true,"DSP_ADVERT_APP_MODIFY":true,"MEDIA_PROTOCOL_VIVE":true,"TOOL_CONVERSION_VIEW":true,"ADVERT_TAG_ADD":true,"MEDIA_APP_AUDIT":true,"MANAGE_USER_MODIFY":true,"ADVERT_APP_ADD":true,"MANAGE_USER_DELETE":true,"DMP_PACK_MODIFY":true,"STRATEGY_MODIFY":true,"STRATEGY_OPERATOR":true,"DSP_ADVERT_AD_ADD":true,"FINANCIAL_MEDIA_VIEW":true,"ADVERT_APP_VIEW":true,"MEDIA_USER_ADD":true,"MANAGE_USER_ADD":true,"DSP_ADVERT_APP_ADD":true,"ADVERT_TAG_VIEW":true,"MEDIA_USER_MODIFY":true,"DMP_PACK_ADD":true,"DSP_ADVERT_ACCOUNT_DELETE":true,"TOOL_CONVERSION_ADD":true,"MEDIA_USER_VIEW":true,"FINANCIAL_MEDIA_EXPORT":true,"DSP_ADVERT_ACCOUNT_MODIFY":true,"FINANCIAL_MEDIA_ADVERTISER_VIEW":true,"DSP_ADVERT_AD_VIEW":true,"FINANCIAL_ADVERTISER_IMPORT":true,"ADVERT_VIEW":true,"STRATEGY_RULE_OPERATOR":true,"FINANCIAL_MEDIA_IMPORT":true,"MANAGE_USER_VIEW":true,"DMP_PACK_VIEW":true,"BASE_APP_INDUSTRY_VIEW":true,"VISITOR_USER_VIEW":true,"DSP_ADVERT_APP_VIEW":true,"STATISTICS_MEDIA_EXPORT":true,"FINANCIAL_ADVERTISER_MODIFY":true,"ADVERT_ADD":true,"FINANCIAL_MEDIA_MODIFY":true,"VISITOR_USER_ADD":true,"STATISTICS_DSP_AD_EXPORT":true,"DSP_ADVERT_AD_DELETE":true,"TOOL_RTB_LOG":true,"ADVERT_MODIFY":true,"MEDIA_TAG_ADD":true,"STRATEGY_RULE_MODIFY":true,"VISITOR_USER_DELETE":true,"MEDIA_APP_ADD":true,"STATISTICS_ADVERT_VIEW":true,"MEDIA_USER_REMOVE":true,"FINANCIAL_ADVERTISER_VIEW":true,"BASE_AREA_MODIFY":true,"DMP_PACK_OPERATOR":true,"TOOL_MAINTAIN_SERVICE_VIEW":true,"FINANCIAL_MEDIA_ADVERTISER_MODIFY":true,"TOOL_MAINTAIN_CACHE_OPER":true,"TOOL_CONVERSION_EXPORT":true,"ADVERT_TAG_MODIFY":true,"FINANCIAL_ADVERTISER_EXPORT":true,"BASE_AREA_ADD":true,"DSP_ADVERT_ACCOUNT_VIEW":true,"MEDIA_PROTOCOL_MODIFY":true,"VISITOR_USER_OPER":true,"TOOL_ERROR_CODE_LOG":true,"MEDIA_APP_MODIFY":true,"BASE_AREA_VIEW":true,"SYSTEM_SETTINGS_VIEW":true,"TOOL_MAINTAIN_VIEW":true,"BASE_APP_INDUSTRY_MODIFY":true,"MANAGE_ROLE_MODIFY":true,"BASE_ERROR_CODE_VIEW":true,"TOOL_CONVERSION_MODIFY":true,"DSP_ADVERT_ACCOUNT_ADD":true,"STRATEGY_RULE_ADD":true,"FINANCIAL_MEDIA_ADVERTISER_EXPORT":true,"MANAGE_ROLE_DELETE":true,"STATISTICS_DSP_AD_VIEW":true,"STRATEGY_RULE_VIEW":true},"lastChangePasswordTime":"2025-04-27 14:22:22","timeout":*************}}]  SUCCESS
[2025-07-07 20:09:27.328] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:09:27 to 2025-07-07 20:09:27] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03","20:04","20:05","20:06","20:07","20:08","20:09"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:09:27.333] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:09:27 to 2025-07-07 20:09:27] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:09:33.186] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/systemSettings/get] stack[cn.taken.ad.api.oper.system.SystemSettingsApi.info] time[2025-07-07 20:09:33 to 2025-07-07 20:09:33] params[] response[{"success":true,"code":"0","message":"success","result":{}}]  SUCCESS
[2025-07-07 20:09:37.094] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:10:01.654] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/systemSettings/set] stack[cn.taken.ad.api.oper.system.SystemSettingsApi.set] time[2025-07-07 20:10:01 to 2025-07-07 20:10:01] params[jsonString= ; ] response[{"success":false,"code":"-1","message":"参数错误","result":null}]  SUCCESS
[2025-07-07 20:10:07.184] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:10:27.088] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/minuteTrend] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.minuteTrend] time[2025-07-07 20:10:27 to 2025-07-07 20:10:27] params[] response[{"success":true,"code":"0","message":"success","result":{"t":"","y":[{"name":"媒体请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"媒体支出","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算请求","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算填充","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算竟胜","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"预算收入","type":"line","data":[0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00,0.00]},{"name":"曝光","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},{"name":"点击","type":"line","data":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}],"x":["19:10","19:11","19:12","19:13","19:14","19:15","19:16","19:17","19:18","19:19","19:20","19:21","19:22","19:23","19:24","19:25","19:26","19:27","19:28","19:29","19:30","19:31","19:32","19:33","19:34","19:35","19:36","19:37","19:38","19:39","19:40","19:41","19:42","19:43","19:44","19:45","19:46","19:47","19:48","19:49","19:50","19:51","19:52","19:53","19:54","19:55","19:56","19:57","19:58","19:59","20:00","20:01","20:02","20:03","20:04","20:05","20:06","20:07","20:08","20:09"],"s":["媒体请求","媒体填充","媒体竟胜","媒体支出","预算请求","预算填充","预算竟胜","预算收入","曝光","点击"]}}]  SUCCESS
[2025-07-07 20:10:27.102] [cn.taken.ad.configuration.web.log.LogAspect.log,185] INFO  - ip[127.0.0.1] user[changyw] uri[/web/o/dashboard/dayTotal] stack[cn.taken.ad.api.oper.pub.OperDashboardApi.todayTotal] time[2025-07-07 20:10:27 to 2025-07-07 20:10:27] params[] response[{"success":true,"code":"0","message":"success","result":[{"statisticsTime":"20250707","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0},{"statisticsTime":"20250706","mediaReqTotal":0,"mediaParticipatingTotal":0,"mediaWinTotal":0,"mediaAmount":0,"advertiserReqTotal":0,"advertiserParticipatingTotal":0,"advertiserWinTotal":0,"advertiserAmount":0.00,"eventExposureTotal":0,"eventClickTotal":0}]}]  SUCCESS
[2025-07-07 20:10:37.271] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
[2025-07-07 20:11:07.362] [cn.taken.ad.configuration.server.ServerInfoManager.reNew,77] INFO  - renew service server WEB , 915d12ede8ff4300af89050c3ba05737 , 26
